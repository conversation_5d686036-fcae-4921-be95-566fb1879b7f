"use client";

import { useState, useEffect } from "react";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import Link from "next/link";
import Head from "next/head";
import {
  ArrowLeftIcon,
  CheckCircleIcon,
  ClockIcon,
  EyeIcon,
  ChatBubbleLeftRightIcon,
  StarIcon,
} from "@heroicons/react/24/outline";
import { CheckCircleIcon as CheckCircleIconSolid } from "@heroicons/react/24/solid";
import { useAuth } from "@/hooks/useAuth";
import { useToast } from "@/components/ToastProvider";
import {
  getHelpRequestDetail,
  createAnswer,
  adoptAnswer,
} from "@/services/helpRequestService";
import {
  HelpRequest,
  HelpAnswer,
  CreateAnswerData,
  PAN_TYPE_MAP,
  RESOURCE_TYPES,
} from "@/types/help-request";
import { generateHelpRequestDetailSEO } from "@/config/help-request-seo";
import {
  HelpRequestPageSEO,
  Breadcrumb,
} from "@/components/help-requests/HelpRequestStructuredData";

// 回答卡片组件
function AnswerCard({
  answer,
  helpRequest,
  onAdopt,
}: {
  answer: HelpAnswer;
  helpRequest: HelpRequest;
  onAdopt: (answerId: number) => void;
}) {
  const { user, isAuthenticated } = useAuth();
  const canAdopt =
    isAuthenticated &&
    user?.id === helpRequest.user_id &&
    helpRequest.status === "open" &&
    !answer.is_best;

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString("zh-CN");
  };

  return (
    <div
      className={`bg-card-background border rounded-lg p-6 ${
        answer.is_best
          ? "border-green-500 bg-green-50 dark:bg-green-900/10"
          : "border-border-color"
      }`}
    >
      {answer.is_best && (
        <div className="flex items-center mb-4 text-green-600">
          <CheckCircleIconSolid className="h-5 w-5 mr-2" />
          <span className="font-medium">最佳答案</span>
        </div>
      )}

      <div className="flex items-start justify-between mb-4">
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-blue-100 dark:bg-blue-900/20 rounded-full flex items-center justify-center">
            <span className="text-blue-600 font-medium">
              {answer.user.username.charAt(0).toUpperCase()}
            </span>
          </div>
          <div>
            <div className="font-medium text-foreground">
              {answer.user.username}
            </div>
            <div className="text-sm text-secondary-text">
              等级 {answer.user.level}{" "}
              {answer.user.title && `· ${answer.user.title}`}
            </div>
          </div>
        </div>
        <div className="text-sm text-secondary-text">
          {formatDate(answer.created_at)}
        </div>
      </div>

      <div className="mb-4">
        <div className="flex items-center mb-2">
          <span className="text-sm font-medium text-foreground mr-2">
            资源链接:
          </span>
          <span
            className={`px-2 py-1 rounded text-xs ${
              PAN_TYPE_MAP[answer.pan_type as keyof typeof PAN_TYPE_MAP] ===
              "百度网盘"
                ? "bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400"
                : PAN_TYPE_MAP[answer.pan_type as keyof typeof PAN_TYPE_MAP] ===
                  "夸克网盘"
                ? "bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-400"
                : PAN_TYPE_MAP[answer.pan_type as keyof typeof PAN_TYPE_MAP] ===
                  "阿里云盘"
                ? "bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-400"
                : "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400"
            }`}
          >
            {PAN_TYPE_MAP[answer.pan_type as keyof typeof PAN_TYPE_MAP]}
          </span>
          {answer.is_parsed && (
            <span className="ml-2 px-2 py-1 bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400 rounded text-xs">
              已解析
            </span>
          )}
        </div>
        <a
          href={answer.resource_link}
          target="_blank"
          rel="noopener noreferrer"
          className="text-blue-600 hover:text-blue-800 break-all"
        >
          {answer.resource_link}
        </a>
      </div>

      {answer.description && (
        <div className="mb-4">
          <div className="text-sm font-medium text-foreground mb-1">说明:</div>
          <p className="text-secondary-text">{answer.description}</p>
        </div>
      )}

      {canAdopt && (
        <div className="flex justify-end">
          <button
            type="button"
            onClick={() => onAdopt(answer.id)}
            className="inline-flex items-center px-3 py-1 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors text-sm"
          >
            <StarIcon className="h-4 w-4 mr-1" />
            采纳为最佳答案
          </button>
        </div>
      )}
    </div>
  );
}

// 回答表单组件
function AnswerForm({
  helpRequestId,
  onAnswerSubmitted,
}: {
  helpRequestId: number;
  onAnswerSubmitted: () => void;
}) {
  const { showToast } = useToast();
  const [formData, setFormData] = useState<
    Omit<CreateAnswerData, "help_request_id">
  >({
    resource_link: "",
    pan_type: 1,
    description: "",
    is_parsed: false,
  });
  const [submitting, setSubmitting] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.resource_link.trim()) {
      showToast("请输入资源链接", "error");
      return;
    }

    setSubmitting(true);
    try {
      const response = await createAnswer({
        ...formData,
        help_request_id: helpRequestId,
      });

      if (response.success) {
        showToast("回答发布成功", "success");
        setFormData({
          resource_link: "",
          pan_type: 1,
          description: "",
          is_parsed: false,
        });
        onAnswerSubmitted();
      } else {
        showToast(response.message || "发布回答失败", "error");
      }
    } catch (error) {
      showToast("发布回答失败", "error");
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <div className="bg-card-background border border-border-color rounded-lg p-6">
      <h3 className="text-lg font-semibold text-foreground mb-4">回答求助</h3>
      <form onSubmit={handleSubmit} className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-foreground mb-2">
            资源链接 *
          </label>
          <input
            type="url"
            value={formData.resource_link}
            onChange={(e) =>
              setFormData({ ...formData, resource_link: e.target.value })
            }
            placeholder="请输入网盘分享链接"
            className="w-full px-3 py-2 border border-border-color rounded-md bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-blue-500"
            required
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-foreground mb-2">
            网盘类型 *
          </label>
          <select
            value={formData.pan_type}
            onChange={(e) =>
              setFormData({ ...formData, pan_type: parseInt(e.target.value) })
            }
            className="w-full px-3 py-2 border border-border-color rounded-md bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-blue-500"
            required
            title="选择网盘类型"
          >
            {Object.entries(PAN_TYPE_MAP).map(([key, value]) => (
              <option key={key} value={key}>
                {value}
              </option>
            ))}
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium text-foreground mb-2">
            资源描述
          </label>
          <textarea
            value={formData.description}
            onChange={(e) =>
              setFormData({ ...formData, description: e.target.value })
            }
            placeholder="可选：描述资源的详细信息、提取码等"
            rows={3}
            className="w-full px-3 py-2 border border-border-color rounded-md bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>

        <div className="flex items-center">
          <input
            type="checkbox"
            id="is_parsed"
            checked={formData.is_parsed}
            onChange={(e) =>
              setFormData({ ...formData, is_parsed: e.target.checked })
            }
            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-border-color rounded"
          />
          <label htmlFor="is_parsed" className="ml-2 text-sm text-foreground">
            资源已解析（无需提取码）
          </label>
        </div>

        <div className="flex justify-end">
          <button
            type="submit"
            disabled={submitting}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            {submitting ? "发布中..." : "发布回答"}
          </button>
        </div>
      </form>
    </div>
  );
}

export default function HelpRequestDetailPage() {
  const params = useParams();
  const router = useRouter();
  const { isAuthenticated } = useAuth();
  const { showToast } = useToast();
  const [helpRequest, setHelpRequest] = useState<HelpRequest | null>(null);
  const [answers, setAnswers] = useState<HelpAnswer[]>([]);
  const [loading, setLoading] = useState(true);

  const helpRequestId = parseInt(params.id as string);

  const loadHelpRequestDetail = async () => {
    setLoading(true);
    try {
      const response = await getHelpRequestDetail(helpRequestId);
      if (response.success) {
        setHelpRequest(response.data.help_request);
        setAnswers(response.data.answers);
      } else {
        showToast(response.error || "获取求助详情失败", "error");
        router.push("/help-requests");
      }
    } catch (error) {
      showToast("获取求助详情失败", "error");
      router.push("/help-requests");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (helpRequestId) {
      loadHelpRequestDetail();
    }
  }, [helpRequestId]);

  const handleAdoptAnswer = async (answerId: number) => {
    try {
      const response = await adoptAnswer(helpRequestId, answerId);
      if (response.success) {
        showToast("答案采纳成功", "success");
        loadHelpRequestDetail(); // 重新加载数据
      } else {
        showToast(response.message || "采纳答案失败", "error");
      }
    } catch (error) {
      showToast("采纳答案失败", "error");
    }
  };

  const handleAnswerSubmitted = () => {
    loadHelpRequestDetail(); // 重新加载数据
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "open":
        return "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400";
      case "solved":
        return "bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400";
      case "closed":
        return "bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400";
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400";
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case "open":
        return "求助中";
      case "solved":
        return "已解决";
      case "closed":
        return "已关闭";
      default:
        return "未知";
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString("zh-CN");
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span className="ml-2 text-secondary-text">加载中...</span>
      </div>
    );
  }

  if (!helpRequest) {
    return (
      <div className="text-center py-12">
        <p className="text-secondary-text">求助不存在</p>
        <Link
          href="/help-requests"
          className="inline-flex items-center px-4 py-2 mt-4 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
        >
          <ArrowLeftIcon className="h-5 w-5 mr-2" />
          返回求助列表
        </Link>
      </div>
    );
  }

  // 面包屑导航数据
  const breadcrumbItems = [
    { name: "首页", url: "/" },
    { name: "资源求助", url: "/help-requests" },
    { name: helpRequest.title },
  ];

  return (
    <>
      {/* SEO和结构化数据 */}
      <Head>
        <title>{generateHelpRequestDetailSEO(helpRequest).title}</title>
        <meta
          name="description"
          content={generateHelpRequestDetailSEO(helpRequest).description}
        />
        <meta
          name="keywords"
          content={generateHelpRequestDetailSEO(helpRequest).keywords}
        />
      </Head>
      <HelpRequestPageSEO
        helpRequest={helpRequest}
        breadcrumbItems={breadcrumbItems}
      />

      <div>
        {/* 面包屑导航 */}
        <Breadcrumb items={breadcrumbItems} className="mb-4" />

        {/* 返回按钮 */}
        <div className="mb-6">
          <Link
            href="/help-requests"
            className="inline-flex items-center text-blue-600 hover:text-blue-800 transition-colors"
          >
            <ArrowLeftIcon className="h-5 w-5 mr-2" />
            返回求助列表
          </Link>
        </div>

        {/* 求助详情 */}
        <div className="bg-card-background border border-border-color rounded-lg p-6 mb-6">
          <div className="flex items-start justify-between mb-4">
            <h1 className="text-2xl font-bold text-foreground">
              {helpRequest.title}
            </h1>
            <span
              className={`px-3 py-1 rounded-full text-sm font-medium whitespace-nowrap ml-4 ${getStatusColor(
                helpRequest.status
              )}`}
            >
              {getStatusText(helpRequest.status)}
            </span>
          </div>

          {helpRequest.description && (
            <div className="mb-4">
              <p className="text-foreground whitespace-pre-wrap">
                {helpRequest.description}
              </p>
            </div>
          )}

          <div className="flex flex-wrap gap-2 mb-4">
            {helpRequest.pan_types.map((panType) => (
              <span
                key={panType}
                className="px-2 py-1 bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400 rounded text-sm"
              >
                {PAN_TYPE_MAP[panType as keyof typeof PAN_TYPE_MAP]}
              </span>
            ))}
            {helpRequest.resource_types?.map((resourceType) => (
              <span
                key={resourceType}
                className="px-2 py-1 bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-400 rounded text-sm"
              >
                {RESOURCE_TYPES.find((rt) => rt.value === resourceType)
                  ?.label || resourceType}
              </span>
            ))}
          </div>

          <div className="flex items-center justify-between text-sm text-secondary-text border-t border-border-color pt-4">
            <div className="flex items-center space-x-6">
              <div className="flex items-center">
                <span className="font-medium">{helpRequest.user.username}</span>
                <span className="ml-2">
                  发布于 {formatDate(helpRequest.created_at)}
                </span>
              </div>
              <div className="flex items-center">
                <EyeIcon className="h-4 w-4 mr-1" />
                <span>{helpRequest.view_count} 次浏览</span>
              </div>
              <div className="flex items-center">
                <ChatBubbleLeftRightIcon className="h-4 w-4 mr-1" />
                <span>{helpRequest.answers_count} 个回答</span>
              </div>
            </div>
          </div>
        </div>

        {/* 回答列表 */}
        <div className="mb-6">
          <h2 className="text-xl font-semibold text-foreground mb-4">
            回答 ({answers.length})
          </h2>
          {answers.length > 0 ? (
            <div className="space-y-4">
              {answers.map((answer) => (
                <AnswerCard
                  key={answer.id}
                  answer={answer}
                  helpRequest={helpRequest}
                  onAdopt={handleAdoptAnswer}
                />
              ))}
            </div>
          ) : (
            <div className="text-center py-8 text-secondary-text">
              暂无回答，快来抢沙发吧！
            </div>
          )}
        </div>

        {/* 回答表单 */}
        {isAuthenticated && helpRequest.status === "open" && (
          <AnswerForm
            helpRequestId={helpRequestId}
            onAnswerSubmitted={handleAnswerSubmitted}
          />
        )}

        {!isAuthenticated && (
          <div className="bg-card-background border border-border-color rounded-lg p-6 text-center">
            <p className="text-secondary-text mb-4">请登录后回答求助</p>
            <Link
              href="/login"
              className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              立即登录
            </Link>
          </div>
        )}
      </div>
    </>
  );
}
