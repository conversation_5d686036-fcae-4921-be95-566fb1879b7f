@import "tailwindcss";

/* 苹果风格的颜色变量 */
:root {
  /* 日间模式 */
  --background: #ffffff;
  --foreground: #18181c;
  --secondary-text: #5c5c66;
  --nav-background: rgba(255, 255, 255, 0.85);
  --card-background: #f8f9fb;
  --hover-background: #e4e6eb;
  --border-color: #d2d2d7;
  --link-color: #0056b3;
  --button-background: #0071e3;
  --button-hover: #0056b3;
  --secondary-button-background: #6b7280;
  --secondary-button-text: #ffffff;
  --secondary-button-hover: #4b5563;
  --footer-background: #f5f5f7;
  --footer-border: #d2d2d7;
  --footer-link: #0066cc;
  --footer-link-hover: #0077ed;
}

.dark {
  /* 暗黑模式 - 采用柔和的黑灰色配色 */
  --background: #1a1a1f;
  --foreground: #f0f0f5;
  --secondary-text: #b0b3b8;
  --nav-background: rgba(32, 32, 38, 0.92);
  --card-background: #242428;
  --hover-background: #2c2c34;
  --border-color: #3a3a42;
  --link-color: #4da3ff;
  --button-background: #2997ff;
  --button-hover: #0077ed;
  --secondary-button-background: #4b5563;
  --secondary-button-text: #f9fafb;
  --secondary-button-hover: #374151;
  --footer-background: #222226;
  --footer-border: #35353d;
  --footer-link: #2997ff;
  --footer-link-hover: #56abff;
}

/* 基础样式 */
body {
  background: var(--background);
  color: var(--foreground);
  font-family: -apple-system, BlinkMacSystemFont, "SF Pro Text",
    "Helvetica Neue", Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  letter-spacing: -0.022em;
}

/* 苹果风格的文本样式 */
h1,
h2,
h3,
h4,
h5,
h6 {
  font-family: "SF Pro Display", -apple-system, BlinkMacSystemFont, sans-serif;
}

/* 暗黑模式样式覆盖 */
.dark .bg-white {
  background-color: var(--card-background);
}

.dark .text-gray-600 {
  color: var(--secondary-text);
}

.dark .text-gray-900 {
  color: var(--foreground);
}

.dark .border-gray-200 {
  border-color: var(--border-color);
}

.dark .bg-gray-100 {
  background-color: var(--hover-background);
}

.dark .hover\:bg-gray-100:hover {
  background-color: var(--hover-background);
}

/* 确保筛选组件在暗黑模式下文字为白色 */
.dark .filter-bar label {
  color: white !important;
}

.dark .filter-bar select,
.dark select option,
.dark #pan-type-select option,
.dark #file-type-select option,
.dark #time-filter-select option {
  color: white !important;
  background-color: rgb(55 65 81) !important; /* gray-700 */
}

/* SearchFilter组件暗夜模式优化 */
.dark .bg-white\/80 {
  background-color: rgba(36, 36, 40, 0.9) !important;
  border-color: rgba(58, 58, 66, 0.5) !important;
}

/* 筛选标签文字颜色 */
.dark .text-gray-700 {
  color: #e5e7eb !important; /* gray-200 */
}

/* 筛选输入框和下拉框 */
.dark .bg-white\/90 {
  background-color: rgba(55, 65, 81, 0.95) !important; /* gray-700 */
  border-color: rgba(75, 85, 99, 0.8) !important; /* gray-600 */
}

.dark .text-gray-900 {
  color: #f3f4f6 !important; /* gray-100 */
}

/* 搜索框图标 */
.dark .text-gray-400 {
  color: #9ca3af !important; /* gray-400 */
}

/* DataTable组件暗夜模式优化 */
.dark .bg-gray-50\/80 {
  background-color: rgba(55, 65, 81, 0.8) !important; /* gray-700 */
}

.dark .bg-white\/90 {
  background-color: rgba(36, 36, 40, 0.9) !important;
}

/* 表格头部文字 */
.dark th.text-gray-700 {
  color: #e5e7eb !important; /* gray-200 */
}

/* 表格内容文字 */
.dark td.text-gray-900 {
  color: #f3f4f6 !important; /* gray-100 */
}

.dark .text-gray-500 {
  color: #d1d5db !important; /* gray-300 */
}

/* 表格边框 */
.dark .border-gray-200\/50 {
  border-color: rgba(75, 85, 99, 0.5) !important; /* gray-600 */
}

.dark .border-gray-200\/60 {
  border-color: rgba(75, 85, 99, 0.6) !important; /* gray-600 */
}

.dark .border-gray-100\/60 {
  border-color: rgba(75, 85, 99, 0.6) !important; /* gray-600 */
}

.dark .divide-gray-200\/60 > * + * {
  border-color: rgba(75, 85, 99, 0.6) !important; /* gray-600 */
}

/* 状态标签暗夜模式优化 */
.dark .bg-blue-100 {
  background-color: rgba(
    59,
    130,
    246,
    0.2
  ) !important; /* blue-500 with opacity */
}

.dark .text-blue-800 {
  color: #93c5fd !important; /* blue-300 */
}

.dark .bg-red-100 {
  background-color: rgba(
    239,
    68,
    68,
    0.2
  ) !important; /* red-500 with opacity */
}

.dark .text-red-800 {
  color: #fca5a5 !important; /* red-300 */
}

.dark .bg-green-100 {
  background-color: rgba(
    34,
    197,
    94,
    0.2
  ) !important; /* green-500 with opacity */
}

.dark .text-green-800 {
  color: #86efac !important; /* green-300 */
}

.dark .bg-yellow-100 {
  background-color: rgba(
    234,
    179,
    8,
    0.2
  ) !important; /* yellow-500 with opacity */
}

.dark .text-yellow-800 {
  color: #fde047 !important; /* yellow-300 */
}

/* 悬停效果优化 */
.dark .hover\:bg-blue-50\/50:hover {
  background-color: rgba(59, 130, 246, 0.1) !important;
}

.dark .hover\:bg-blue-50\/80:hover {
  background-color: rgba(59, 130, 246, 0.15) !important;
}

.dark .hover\:bg-red-50\/80:hover {
  background-color: rgba(239, 68, 68, 0.15) !important;
}

.dark .hover\:bg-green-50\/80:hover {
  background-color: rgba(34, 197, 94, 0.15) !important;
}

/* 资源失效反馈弹窗中的下拉列表样式 */
.dark select option {
  background-color: rgb(55 65 81) !important; /* gray-700 */
  color: white !important;
}

/* 更具体的选择器确保弹窗中的下拉列表样式正确 */
.dark [role="dialog"] select,
.dark [role="dialog"] select option {
  background-color: rgb(55 65 81) !important; /* gray-700 */
  color: white !important;
}

/* 精确搜索按钮文字颜色 - 使用更精确的选择器 */
.dark .filter-bar .inline-flex span {
  color: white !important;
}

/* 移除暗黑模式下筛选组件的聚焦效果和文字装饰 */
.dark .filter-bar select:focus,
.dark .filter-bar label:focus,
.dark #pan-type-select:focus,
.dark #file-type-select:focus,
.dark #time-filter-select:focus {
  outline: none !important;
  box-shadow: none !important;
  text-decoration: none !important;
  border-color: var(--border-color) !important;
}

/* 确保标签文字没有任何装饰效果 */
.dark .filter-bar label {
  text-decoration: none !important;
  border-bottom: none !important;
  box-shadow: none !important;
}

/* 毛玻璃效果 */
.glass-effect {
  -webkit-backdrop-filter: saturate(180%) blur(20px);
  backdrop-filter: saturate(180%) blur(20px);
}

/* 平滑过渡 */
* {
  transition: background-color 0.3s ease, color 0.3s ease,
    border-color 0.3s ease;
}

/* 导航栏样式 */
.nav-transparent {
  background-color: transparent;
}

.nav-scrolled {
  background-color: var(--nav-background);
}

/* 资源数字值样式 */
.numeric-value {
  color: #18181c;
  font-weight: 700;
}

.dark .numeric-value {
  color: #f5f6fa;
}

/* 黑暗模式下的文字颜色 */
.dark .dark-text-white {
  color: white !important;
}

/* 滚动按钮样式 */
.scroll-button-bg {
  background-color: rgba(229, 231, 235, 0.8);
}

.dark .scroll-button-bg {
  background-color: rgba(55, 65, 81, 0.8);
}

/* 页脚样式 */
.apple-footer {
  background-color: var(--footer-background);
  color: var(--secondary-text);
  font-size: 0.85rem;
  border-top: 1px solid var(--footer-border);
  padding: 1rem 0;
}

.apple-footer h3 {
  font-weight: 600;
  font-size: 0.9rem;
  margin-bottom: 0.5rem;
  color: var(--foreground);
}

.apple-footer-links a {
  color: var(--footer-link);
  transition: color 0.2s ease;
  display: inline-block;
  padding: 0.2rem 0;
}

.apple-footer-links a:hover {
  color: var(--footer-link-hover);
  text-decoration: none;
}

.apple-footer-copyright {
  font-size: 0.75rem;
  margin-top: 0.5rem;
}

/* 搜索提示按钮在暗黑模式下为灰白色 */
.dark .search-tips-btn {
  color: #e5e7eb !important;
}

/* 资源卡片反馈按钮特定样式 - 覆盖全局规则 */
.dark .resource-feedback-btn {
  background-color: #f9f9f9 !important;
  color: #374151 !important;
}

.dark .resource-feedback-btn:hover {
  background-color: #e5e7eb !important;
}

/* 网盘类型标签特定样式 - 覆盖全局规则 */
.dark .pan-type-label {
  background-color: #f9f9f9 !important;
  color: #374151 !important;
}

/* 确保反馈按钮内的span文字颜色正确 */
.dark .resource-feedback-btn span {
  color: #374151 !important;
}

/* 管理后台专用样式 */
.admin-layout {
  min-height: 100vh;
}

/* 管理后台侧边栏样式 */
.admin-sidebar {
  transition: transform 0.3s ease-in-out;
}

/* 管理后台布局优化 */
.admin-layout-wrapper {
  height: 100vh;
  overflow: hidden;
}

/* 管理后台主内容区域 */
.admin-main-content {
  height: calc(100vh - 4rem); /* 减去顶部导航栏高度 */
  overflow-y: auto;
}

/* 管理后台页面内容 */
.admin-page-content {
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* 管理后台侧边栏折叠动画 */
.admin-sidebar-collapsed {
  width: 4rem;
}

.admin-sidebar-expanded {
  width: 16rem;
}

/* 侧边栏tooltip样式优化 */
.admin-sidebar-tooltip {
  position: absolute;
  left: 100%;
  margin-left: 0.5rem;
  padding: 0.5rem;
  background-color: rgba(0, 0, 0, 0.9);
  color: white;
  font-size: 0.875rem;
  border-radius: 0.375rem;
  opacity: 0;
  transition: opacity 0.2s ease-in-out;
  pointer-events: none;
  white-space: nowrap;
  z-index: 50;
}

.group:hover .admin-sidebar-tooltip {
  opacity: 1;
}

/* 管理后台表格样式 */
.admin-table {
  min-width: 800px;
}

/* 管理后台表格响应式 */
@media (max-width: 768px) {
  .admin-table-container {
    overflow-x: auto;
  }

  .admin-table {
    min-width: 600px; /* 进一步减小移动端最小宽度 */
  }

  .admin-table th,
  .admin-table td {
    min-width: 60px;
    white-space: nowrap;
    padding: 0.25rem 0.5rem 0.25rem 0;
    font-size: 0.75rem;
  }

  .admin-table .actions-column {
    min-width: 160px;
  }

  /* 移动端表格优化 */
  .admin-table-mobile {
    font-size: 0.75rem;
  }

  .admin-table-mobile th {
    padding: 0.5rem 0.25rem 0.5rem 0;
    font-size: 0.625rem;
  }

  .admin-table-mobile td {
    padding: 0.5rem 0.25rem 0.5rem 0;
  }

  /* 移动端侧边栏优化 */
  .admin-sidebar-mobile {
    width: 85vw;
    max-width: 320px;
  }

  /* 移动端按钮优化 */
  .admin-button-mobile {
    min-height: 40px;
    padding: 0.5rem 0.75rem;
    touch-action: manipulation;
    font-size: 0.75rem;
  }

  /* 移动端表格滚动优化 */
  .admin-table-scroll {
    overflow-x: auto;
    scroll-behavior: smooth;
  }

  .admin-table-scroll::-webkit-scrollbar {
    height: 6px;
  }

  .admin-table-scroll::-webkit-scrollbar-track {
    background: var(--hover-background);
    border-radius: 3px;
  }

  .admin-table-scroll::-webkit-scrollbar-thumb {
    background: var(--border-color);
    border-radius: 3px;
  }

  .admin-table-scroll::-webkit-scrollbar-thumb:hover {
    background: var(--secondary-text);
  }
}

/* 极小屏幕优化 (320px - 480px) */
@media (max-width: 480px) {
  .admin-table {
    min-width: 500px;
  }

  .admin-table th,
  .admin-table td {
    min-width: 50px;
    padding: 0.25rem 0.25rem 0.25rem 0;
    font-size: 0.625rem;
  }

  .admin-table .actions-column {
    min-width: 140px;
  }

  .admin-button-mobile {
    min-height: 36px;
    padding: 0.5rem;
    font-size: 0.625rem;
  }
}

/* 管理后台模态框移动端适配 */
@media (max-width: 640px) {
  .admin-modal {
    margin: 1rem;
    max-height: calc(100vh - 2rem);
  }
}

/* 管理后台卡片网格响应式 */
.admin-stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
}

@media (max-width: 640px) {
  .admin-stats-grid {
    grid-template-columns: 1fr;
  }
}

/* 管理后台搜索筛选响应式 */
.admin-filter-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

@media (max-width: 640px) {
  .admin-filter-grid {
    grid-template-columns: 1fr;
  }
}

/* 管理后台布局样式 */
.admin-layout-container {
  min-height: calc(100vh - 3rem); /* 移动端：减去导航栏高度 */
}

@media (min-width: 768px) {
  .admin-layout-container {
    min-height: calc(100vh - 3.5rem); /* 桌面端：减去导航栏高度 */
  }
}

/* 移动端侧边栏和主内容优化 */
@media (max-width: 767px) {
  /* 确保主内容区域滚动正常 */
  .admin-main-content {
    position: relative;
    z-index: 1;
    overflow-y: auto;
    overflow-x: hidden;
    height: 100%;
  }

  /* 移动端侧边栏背景修复 */
  .admin-sidebar-mobile-solid {
    background-color: var(--card-background) !important;
    opacity: 1 !important;
    -webkit-backdrop-filter: none;
    backdrop-filter: none;
  }

  /* 确保侧边栏内容也有正确的背景 */
  .admin-sidebar-mobile-solid > * {
    background-color: transparent;
  }

  /* 侧边栏本体样式 */
  .admin-sidebar-mobile {
    position: fixed;
    top: 3rem;
    left: 0;
    bottom: 0;
    width: 16rem; /* 64 * 0.25rem = 16rem */
    max-width: 85vw;
    z-index: 50;
    box-shadow: 0 10px 25px -3px rgba(0, 0, 0, 0.1),
      0 4px 6px -2px rgba(0, 0, 0, 0.05);
  }

  /* 移动端内容容器优化 */
  .admin-content-container {
    max-width: 100vw;
    overflow-x: hidden;
  }

  /* 移动端表单和按钮优化 */
  .admin-form-mobile {
    width: 100%;
    max-width: none;
  }

  .admin-form-mobile input,
  .admin-form-mobile select,
  .admin-form-mobile textarea {
    width: 100%;
    min-height: 44px;
    font-size: 16px; /* 防止iOS缩放 */
  }
}

/* 配置管理界面样式 - 苹果设计系统 */
.config-management {
  font-family: -apple-system, BlinkMacSystemFont, "SF Pro Text", "Segoe UI",
    Roboto, sans-serif;
  background: var(--background);
}

.config-tree {
  background: var(--card-background);
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
  border: 1px solid var(--border-color);
}

.config-tree-node {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.config-tree-node:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.config-search {
  background: var(--card-background);
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
  border: 1px solid var(--border-color);
}

.config-editor {
  background: var(--card-background);
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15), 0 2px 4px rgba(0, 0, 0, 0.1);
  border: 1px solid var(--border-color);
}

/* 配置值显示样式 */
.config-value {
  font-family: "SF Mono", Monaco, "Cascadia Code", "Roboto Mono", Consolas,
    "Courier New", monospace;
  background: rgba(0, 122, 255, 0.1);
  border-radius: 6px;
  padding: 2px 6px;
  font-size: 0.875rem;
  color: #007aff;
  border: 1px solid rgba(0, 122, 255, 0.2);
}

.dark .config-value {
  background: rgba(41, 151, 255, 0.15);
  color: #2997ff;
  border-color: rgba(41, 151, 255, 0.3);
}

/* 配置类型标签样式 */
.config-type-tag {
  background: linear-gradient(135deg, #007aff, #5856d6);
  color: white;
  border-radius: 8px;
  padding: 2px 8px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* 敏感配置标识样式 */
.config-sensitive-tag {
  background: linear-gradient(135deg, #ff3b30, #ff9500);
  color: white;
  border-radius: 8px;
  padding: 2px 8px;
  font-size: 0.75rem;
  font-weight: 600;
  animation: pulse 2s infinite;
  box-shadow: 0 1px 2px rgba(255, 59, 48, 0.3);
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.8;
  }
}

/* 配置效果类型样式 */
.config-effect-immediate {
  color: #34c759;
  font-weight: 600;
}

.config-effect-restart {
  color: #ff3b30;
  font-weight: 600;
}

.config-effect-reload {
  color: #ff9500;
  font-weight: 600;
}

.config-effect-manual {
  color: #007aff;
  font-weight: 600;
}

/* 配置搜索结果样式 */
.config-search-result {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  border-radius: 8px;
}

.config-search-result:hover {
  background: var(--hover-background);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 配置编辑器表单样式 */
.config-editor input,
.config-editor textarea,
.config-editor select {
  border-radius: 8px;
  border: 1px solid var(--border-color);
  background: var(--background);
  color: var(--foreground);
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.config-editor input:focus,
.config-editor textarea:focus,
.config-editor select:focus {
  border-color: #007aff;
  box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1);
  outline: none;
}

/* 配置管理响应式设计 */
@media (max-width: 768px) {
  .config-management {
    padding: 0;
  }

  .config-tree,
  .config-search {
    border-radius: 8px;
    margin-bottom: 1rem;
  }

  .config-tree-node {
    padding: 0.75rem;
  }

  .config-search-result {
    padding: 1rem;
  }

  /* 移动端配置编辑器优化 - 保持在标准布局内 */
  .config-editor {
    border-radius: 8px;
    margin-top: 1rem;
  }
}

/* 配置树节点层级缩进 */
.config-tree-level-0 {
  padding-left: 12px;
}
.config-tree-level-1 {
  padding-left: 32px;
}
.config-tree-level-2 {
  padding-left: 52px;
}
.config-tree-level-3 {
  padding-left: 72px;
}
.config-tree-level-4 {
  padding-left: 92px;
}
.config-tree-level-5 {
  padding-left: 112px;
}
.config-tree-level-6 {
  padding-left: 132px;
}
.config-tree-level-7 {
  padding-left: 152px;
}
.config-tree-level-8 {
  padding-left: 172px;
}
.config-tree-level-9 {
  padding-left: 192px;
}
.config-tree-level-10 {
  padding-left: 212px;
}

/* 配置验证状态样式 */
.config-validation-success {
  background: rgba(52, 199, 89, 0.1);
  border-color: #34c759;
  color: #34c759;
}

.config-validation-error {
  background: rgba(255, 59, 48, 0.1);
  border-color: #ff3b30;
  color: #ff3b30;
}

/* 配置管理工具栏样式 */
.config-toolbar {
  background: var(--card-background);
  border-bottom: 1px solid var(--border-color);
  -webkit-backdrop-filter: blur(20px);
  backdrop-filter: blur(20px);
}
