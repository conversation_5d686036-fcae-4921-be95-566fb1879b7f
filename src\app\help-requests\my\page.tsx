"use client";

import { useState, useEffect, useCallback } from "react";
import Link from "next/link";
import {
  PlusIcon,
  EyeIcon,
  ChatBubbleLeftRightIcon,
} from "@heroicons/react/24/outline";

import { useToast } from "@/components/ToastProvider";
import AuthGuard from "@/components/AuthGuard";
import {
  getUserHelpRequests,
  getUserAnswers,
  getUserStats,
} from "@/services/helpRequestService";
import {
  HelpRequest,
  HelpAnswer,
  PAN_TYPE_MAP,
  RESOURCE_TYPES,
} from "@/types/help-request";
import { UserStats } from "@/types/user-level";
import Pagination from "@/components/Pagination";

// 用户统计卡片组件
function UserStatsCard({ stats }: { stats: UserStats | null }) {
  if (!stats) return null;

  return (
    <div className="bg-card-background border border-border-color rounded-lg p-6 mb-6">
      <h2 className="text-lg font-semibold text-foreground mb-4">我的统计</h2>
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <div className="text-center">
          <div className="text-2xl font-bold text-blue-600">
            {stats.help_requests_count}
          </div>
          <div className="text-sm text-secondary-text">发布求助</div>
        </div>
        <div className="text-center">
          <div className="text-2xl font-bold text-green-600">
            {stats.answers_count}
          </div>
          <div className="text-sm text-secondary-text">回答数量</div>
        </div>
        <div className="text-center">
          <div className="text-2xl font-bold text-yellow-600">
            {stats.best_answers_count}
          </div>
          <div className="text-sm text-secondary-text">最佳答案</div>
        </div>
        <div className="text-center">
          <div className="text-2xl font-bold text-purple-600">
            {stats.points}
          </div>
          <div className="text-sm text-secondary-text">积分</div>
        </div>
      </div>
      <div className="mt-4 pt-4 border-t border-border-color">
        <div className="flex items-center justify-between">
          <div>
            <span className="text-sm text-secondary-text">当前等级: </span>
            <span
              className="font-medium"
              // 动态颜色必须使用内联样式
              style={{ color: stats.level.color }}
            >
              {stats.level.icon} {stats.level.title}
            </span>
          </div>
          {stats.next_level && (
            <div className="text-sm text-secondary-text">
              距离下一等级还需 {stats.points_to_next_level} 积分
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

// 我的求助卡片组件
function MyHelpRequestCard({ helpRequest }: { helpRequest: HelpRequest }) {
  const getStatusColor = (status: string) => {
    switch (status) {
      case "open":
        return "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400";
      case "solved":
        return "bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400";
      case "closed":
        return "bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400";
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400";
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case "open":
        return "求助中";
      case "solved":
        return "已解决";
      case "closed":
        return "已关闭";
      default:
        return "未知";
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffTime = Math.abs(now.getTime() - date.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays === 1) {
      return "今天";
    } else if (diffDays <= 7) {
      return `${diffDays}天前`;
    } else {
      return date.toLocaleDateString("zh-CN");
    }
  };

  return (
    <div className="bg-card-background border border-border-color rounded-lg p-6 hover:shadow-md transition-shadow">
      <div className="flex items-start justify-between mb-3">
        <Link
          href={`/help-requests/${helpRequest.id}`}
          className="text-lg font-semibold text-foreground hover:text-blue-600 transition-colors line-clamp-2 flex-1"
        >
          {helpRequest.title}
        </Link>
        <span
          className={`px-2 py-1 rounded-full text-xs font-medium whitespace-nowrap ml-3 ${getStatusColor(
            helpRequest.status
          )}`}
        >
          {getStatusText(helpRequest.status)}
        </span>
      </div>

      {helpRequest.description && (
        <p className="text-secondary-text text-sm mb-3 line-clamp-2">
          {helpRequest.description}
        </p>
      )}

      <div className="flex flex-wrap gap-2 mb-3">
        {helpRequest.pan_types.map((panType) => (
          <span
            key={panType}
            className="px-2 py-1 bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400 rounded text-xs"
          >
            {PAN_TYPE_MAP[panType as keyof typeof PAN_TYPE_MAP]}
          </span>
        ))}
        {helpRequest.resource_types?.map((resourceType) => (
          <span
            key={resourceType}
            className="px-2 py-1 bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-400 rounded text-xs"
          >
            {RESOURCE_TYPES.find((rt) => rt.value === resourceType)?.label ||
              resourceType}
          </span>
        ))}
      </div>

      <div className="flex items-center justify-between text-sm text-secondary-text">
        <div className="flex items-center space-x-4">
          <div className="flex items-center">
            <ChatBubbleLeftRightIcon className="h-4 w-4 mr-1" />
            <span>{helpRequest.answers_count} 个回答</span>
          </div>
          <div className="flex items-center">
            <EyeIcon className="h-4 w-4 mr-1" />
            <span>{helpRequest.view_count} 次浏览</span>
          </div>
        </div>
        <span>{formatDate(helpRequest.created_at)}</span>
      </div>
    </div>
  );
}

// 我的回答卡片组件
function MyAnswerCard({ answer }: { answer: HelpAnswer }) {
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("zh-CN");
  };

  return (
    <div
      className={`bg-card-background border rounded-lg p-4 ${
        answer.is_best
          ? "border-green-500 bg-green-50 dark:bg-green-900/10"
          : "border-border-color"
      }`}
    >
      {answer.is_best && (
        <div className="flex items-center mb-2 text-green-600">
          <span className="text-sm font-medium">🌟 最佳答案</span>
        </div>
      )}

      <Link
        href={`/help-requests/${answer.help_request_id}`}
        className="text-foreground hover:text-blue-600 transition-colors font-medium"
      >
        回答了求助问题
      </Link>

      <div className="mt-2 text-sm text-secondary-text">
        <div className="flex items-center mb-1">
          <span className="mr-2">网盘类型:</span>
          <span className="px-2 py-1 bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400 rounded text-xs">
            {PAN_TYPE_MAP[answer.pan_type as keyof typeof PAN_TYPE_MAP]}
          </span>
          {answer.is_parsed && (
            <span className="ml-2 px-2 py-1 bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400 rounded text-xs">
              已解析
            </span>
          )}
        </div>
        {answer.description && (
          <p className="text-secondary-text line-clamp-2 mb-2">
            {answer.description}
          </p>
        )}
        <div className="text-xs text-secondary-text">
          回答时间: {formatDate(answer.created_at)}
        </div>
      </div>
    </div>
  );
}

function MyHelpRequestsContent() {
  const { showToast } = useToast();
  const [activeTab, setActiveTab] = useState<"requests" | "answers">(
    "requests"
  );
  const [helpRequests, setHelpRequests] = useState<HelpRequest[]>([]);
  const [answers, setAnswers] = useState<HelpAnswer[]>([]);
  const [userStats, setUserStats] = useState<UserStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [total, setTotal] = useState(0);

  const pageSize = 20;

  const loadUserStats = useCallback(async () => {
    try {
      const response = await getUserStats();
      if (response.success && response.data) {
        setUserStats(response.data);
      }
    } catch {
      console.error("获取用户统计失败");
    }
  }, []);

  const loadHelpRequests = useCallback(async () => {
    setLoading(true);
    try {
      const response = await getUserHelpRequests(currentPage, pageSize);
      if (response.success) {
        setHelpRequests(response.data.help_requests);
        setTotalPages(response.data.total_pages);
        setTotal(response.data.total);
      } else {
        showToast(response.error || "获取我的求助失败", "error");
      }
    } catch {
      showToast("获取我的求助失败", "error");
    } finally {
      setLoading(false);
    }
  }, [currentPage, showToast]);

  const loadAnswers = useCallback(async () => {
    setLoading(true);
    try {
      const response = await getUserAnswers(currentPage, pageSize);
      if (response.success) {
        setAnswers(response.data.answers);
        setTotalPages(response.data.total_pages);
        setTotal(response.data.total);
      } else {
        showToast(response.error || "获取我的回答失败", "error");
      }
    } catch {
      showToast("获取我的回答失败", "error");
    } finally {
      setLoading(false);
    }
  }, [currentPage, showToast]);

  useEffect(() => {
    loadUserStats();
  }, [loadUserStats]);

  useEffect(() => {
    if (activeTab === "requests") {
      loadHelpRequests();
    } else {
      loadAnswers();
    }
  }, [activeTab, loadHelpRequests, loadAnswers]);

  const handleTabChange = (tab: "requests" | "answers") => {
    setActiveTab(tab);
    setCurrentPage(1);
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    window.scrollTo(0, 0);
  };

  return (
    <div>
      {/* 页面头部 */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h1 className="text-2xl font-bold text-foreground">我的求助</h1>
          <p className="text-secondary-text mt-1">管理您的求助和回答</p>
        </div>
        <Link
          href="/help-requests/create"
          className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
        >
          <PlusIcon className="h-5 w-5 mr-2" />
          发布求助
        </Link>
      </div>

      {/* 用户统计 */}
      <UserStatsCard stats={userStats} />

      {/* 标签页 */}
      <div className="mb-6">
        <div className="border-b border-border-color">
          <nav className="-mb-px flex space-x-8">
            <button
              type="button"
              onClick={() => handleTabChange("requests")}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === "requests"
                  ? "border-blue-500 text-blue-600"
                  : "border-transparent text-secondary-text hover:text-foreground hover:border-gray-300"
              }`}
            >
              我的求助
            </button>
            <button
              type="button"
              onClick={() => handleTabChange("answers")}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === "answers"
                  ? "border-blue-500 text-blue-600"
                  : "border-transparent text-secondary-text hover:text-foreground hover:border-gray-300"
              }`}
            >
              我的回答
            </button>
          </nav>
        </div>
      </div>

      {/* 统计信息 */}
      {!loading && (
        <div className="mb-4 text-sm text-secondary-text">
          共 <span className="font-semibold text-foreground">{total}</span>{" "}
          条记录
        </div>
      )}

      {/* 内容区域 */}
      {loading ? (
        <div className="flex justify-center items-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <span className="ml-2 text-secondary-text">加载中...</span>
        </div>
      ) : (
        <>
          {activeTab === "requests" ? (
            helpRequests.length > 0 ? (
              <div className="space-y-4">
                {helpRequests.map((helpRequest) => (
                  <MyHelpRequestCard
                    key={helpRequest.id}
                    helpRequest={helpRequest}
                  />
                ))}
              </div>
            ) : (
              <div className="text-center py-12">
                <p className="text-secondary-text mb-4">您还没有发布过求助</p>
                <Link
                  href="/help-requests/create"
                  className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                >
                  <PlusIcon className="h-5 w-5 mr-2" />
                  发布第一个求助
                </Link>
              </div>
            )
          ) : answers.length > 0 ? (
            <div className="space-y-4">
              {answers.map((answer) => (
                <MyAnswerCard key={answer.id} answer={answer} />
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <p className="text-secondary-text mb-4">您还没有回答过求助</p>
              <Link
                href="/help-requests"
                className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                去帮助他人
              </Link>
            </div>
          )}
        </>
      )}

      {/* 分页 */}
      {!loading && totalPages > 1 && (
        <div className="mt-8">
          <Pagination
            currentPage={currentPage}
            totalPages={totalPages}
            onPageChange={handlePageChange}
          />
        </div>
      )}
    </div>
  );
}

export default function MyHelpRequestsPage() {
  return (
    <AuthGuard requireAuth={true}>
      <MyHelpRequestsContent />
    </AuthGuard>
  );
}
