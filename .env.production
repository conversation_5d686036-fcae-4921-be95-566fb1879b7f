# 生产环境配置
# 前端网站的公开访问地址
NEXT_PUBLIC_API_BASE_URL=https://pansoo.cn

# 后端API服务器地址（用于服务器端API代理）
# 通过nginx代理访问后端服务，nginx监听80端口并转发到内部9999端口
API_PROXY_TARGET=http://14.103.247.233

# 链接处理配置 - 生产环境可根据需要调整
NEXT_PUBLIC_ENABLE_GET_SHARE_API=true
NEXT_PUBLIC_GET_SHARE_TYPES=quark,baidu

# ===== 用户认证功能配置 =====

# 是否启用用户注册功能
# true: 启用注册功能
# false: 禁用注册功能（默认）
NEXT_PUBLIC_ENABLE_REGISTRATION=false

# 是否在导航栏显示注册按钮
# true: 显示注册按钮
# false: 隐藏注册按钮（默认）
NEXT_PUBLIC_SHOW_REGISTRATION_IN_NAV=false

# 是否在登录页面显示注册链接
# true: 显示注册链接
# false: 隐藏注册链接（默认）
NEXT_PUBLIC_SHOW_REGISTRATION_IN_LOGIN=false

# 是否在导航栏显示登录按钮
# true: 显示登录按钮（默认）
# false: 隐藏登录按钮
NEXT_PUBLIC_SHOW_LOGIN_IN_NAV=false