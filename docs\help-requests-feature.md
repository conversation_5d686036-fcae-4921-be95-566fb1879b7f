# 资源求助功能文档

## 功能概述

资源求助功能是97盘搜的社区互助功能，允许用户发布资源求助，其他用户可以提供帮助并分享资源链接。该功能旨在建立一个互助的资源分享社区。

## 功能特性

### 核心功能
- ✅ 发布资源求助
- ✅ 回答求助问题
- ✅ 采纳最佳答案
- ✅ 用户等级系统
- ✅ 积分奖励机制
- ✅ 权限控制
- ✅ 管理员后台管理

### 用户权限
- **游客用户**：可浏览求助列表和详情页面
- **登录用户**：可发布求助、回答求助、采纳答案
- **管理员用户**：可删除求助帖子、管理用户

### 支持的网盘类型
- 百度网盘
- 夸克网盘
- 阿里云盘
- 迅雷网盘

### 支持的资源类型
- 视频
- 音频
- 图片
- 文档
- 压缩包
- 应用软件
- 游戏
- 电子书
- 其他

## 页面结构

```
/help-requests                    # 求助列表页面
├── /create                       # 发布求助页面
├── /my                          # 我的求助页面
└── /[id]                        # 求助详情页面
```

## 技术实现

### 前端技术栈
- **框架**: Next.js 14 (App Router)
- **语言**: TypeScript
- **样式**: Tailwind CSS
- **状态管理**: React Hooks
- **图标**: Heroicons

### 核心组件
```
src/components/help-requests/
├── HelpRequestCard.tsx          # 求助卡片组件
├── HelpRequestFilters.tsx       # 筛选器组件
├── HelpRequestAuthGuard.tsx     # 权限控制组件
├── HelpRequestStructuredData.tsx # SEO结构化数据组件
└── UserBadge.tsx               # 用户头衔/等级组件
```

### 类型定义
```
src/types/
├── help-request.ts             # 求助相关类型
└── user-level.ts              # 用户等级系统类型
```

### 服务层
```
src/services/
└── helpRequestService.ts       # 求助相关API服务
```

## API接口设计

### 求助相关接口
```typescript
GET    /api/help-requests              # 获取求助列表
POST   /api/help-requests              # 创建求助
GET    /api/help-requests/:id          # 获取求助详情
PUT    /api/help-requests/:id          # 更新求助
DELETE /api/help-requests/:id          # 删除求助

POST   /api/help-requests/:id/answers  # 创建回答
PUT    /api/help-requests/:id/adopt    # 采纳答案
DELETE /api/answers/:id                # 删除回答
```

### 用户相关接口
```typescript
GET    /api/user/help-requests         # 获取我的求助
GET    /api/user/answers               # 获取我的回答
GET    /api/user/stats                 # 获取用户统计
GET    /api/user/points-history        # 获取积分记录
```

### 管理员接口
```typescript
GET    /api/admin/help-requests        # 管理员获取求助列表
DELETE /api/admin/help-requests/:id    # 管理员删除求助
POST   /api/admin/help-requests/batch-delete # 批量删除
GET    /api/admin/help-requests/stats  # 获取统计信息
```

## 用户等级系统

### 等级定义
| 等级 | 名称 | 积分范围 | 图标 | 颜色 |
|------|------|----------|------|------|
| 1 | 新手 | 0-99 | 🌱 | #9CA3AF |
| 2 | 初学者 | 100-299 | 🌿 | #10B981 |
| 3 | 活跃用户 | 300-699 | ⭐ | #3B82F6 |
| 4 | 资深用户 | 700-1499 | 💎 | #8B5CF6 |
| 5 | 专家 | 1500-2999 | 👑 | #F59E0B |
| 6 | 大师 | 3000+ | 🏆 | #EF4444 |

### 积分规则
| 操作 | 获得积分 |
|------|----------|
| 发布求助 | +5 |
| 回答求助 | +10 |
| 答案被采纳 | +50 |
| 求助被解决 | +20 |
| 每日登录 | +2 |
| 完善资料 | +30 |

## SEO优化

### 页面SEO
- 动态生成页面标题和描述
- 关键词优化
- Open Graph标签
- Twitter Card标签
- 规范化URL

### 结构化数据
- Question类型（求助详情）
- ItemList类型（求助列表）
- BreadcrumbList类型（面包屑导航）
- Organization类型（组织信息）
- SearchAction类型（搜索功能）

## 权限控制

### 权限级别
```typescript
export const HELP_REQUEST_PERMISSIONS = {
  VIEW: 'help_request:view',           // 查看求助（游客可访问）
  CREATE: 'help_request:create',       // 发布求助（需登录）
  ANSWER: 'help_request:answer',       // 回答求助（需登录）
  ADOPT: 'help_request:adopt',         // 采纳答案（求助者）
  DELETE: 'help_request:delete',       // 删除求助（管理员或求助者）
  MODERATE: 'help_request:moderate',   # 管理求助（管理员）
} as const;
```

### 权限检查
- 使用`HelpRequestAuthGuard`组件进行权限控制
- 使用`useHelpRequestPermission` Hook检查权限
- 提供`PermissionButton`和`PermissionLink`组件

## 响应式设计

### 移动端适配
- 求助卡片在移动端采用单列布局
- 表单在移动端优化输入体验
- 筛选器在移动端采用折叠式设计
- 管理后台表格支持横向滚动

### 主题支持
- 完全兼容明暗主题切换
- 使用CSS变量确保主题一致性
- 所有组件都支持`dark:`前缀的样式

## 使用指南

### 发布求助
1. 登录账户
2. 点击"发布求助"按钮
3. 填写资源名称（必填）
4. 选择网盘类型（必填，可多选）
5. 选择资源类型（可选）
6. 填写资源描述（可选）
7. 提交求助

### 回答求助
1. 登录账户
2. 浏览求助列表或搜索特定求助
3. 点击进入求助详情页面
4. 填写资源链接（必填）
5. 选择网盘类型（必填）
6. 填写资源描述（可选）
7. 选择是否已解析
8. 提交回答

### 采纳答案
1. 作为求助发布者，查看求助详情页面
2. 浏览所有回答
3. 点击最满意回答的"采纳为最佳答案"按钮
4. 确认采纳

### 管理员操作
1. 登录管理员账户
2. 进入管理后台
3. 点击"求助管理"菜单
4. 查看、搜索、筛选求助
5. 删除不当求助内容

## 集成说明

### 搜索结果集成
当搜索结果少于30条时，会显示求助提示：
- 显示"未找到想要的资源？"提示
- 提供"发布求助"按钮
- 自动填充搜索关键词作为求助标题

### 导航集成
在主导航栏添加"资源求助"菜单项：
- 桌面端导航
- 移动端导航
- 面包屑导航

## 测试

### 单元测试
```bash
npm test src/tests/help-requests.test.ts
```

### 测试覆盖
- 类型定义验证
- 表单验证逻辑
- 权限控制逻辑
- SEO元数据生成
- API响应处理

## 部署注意事项

### 环境变量
确保设置以下环境变量：
```env
NEXT_PUBLIC_API_BASE_URL=your_api_base_url
```

### 数据库
需要创建相应的数据库表：
- help_requests（求助表）
- help_answers（回答表）
- user_points（积分记录表）
- user_levels（用户等级表）

### 权限配置
确保后端API正确实现权限验证：
- JWT token验证
- 用户角色检查
- 操作权限验证

## 后续优化建议

### 功能增强
- [ ] 添加求助标签系统
- [ ] 实现求助收藏功能
- [ ] 添加求助分享功能
- [ ] 实现求助推荐算法
- [ ] 添加求助统计图表

### 性能优化
- [ ] 实现虚拟滚动
- [ ] 添加图片懒加载
- [ ] 优化API缓存策略
- [ ] 实现离线支持

### 用户体验
- [ ] 添加实时通知
- [ ] 实现拖拽上传
- [ ] 添加快捷键支持
- [ ] 优化加载状态

## 联系方式

如有问题或建议，请联系开发团队。
