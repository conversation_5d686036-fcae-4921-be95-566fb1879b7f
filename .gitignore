# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.*
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/versions

# testing
/coverage

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# env files (can opt-in for committing if needed)
.env.local
.env*.local

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts


# 忽略 __pycache__ 目录
pan-so-backend/__pycache__/


/.idea/


db.sqlite3-shm
db.sqlite3-wal
pan-so-backend/__pycache__/
pan-so-backend/__pycache__/models.cpython-311.pyc
pan-so-backend/__pycache__/panku8_crawler.cpython-311.pyc
db.sqlite3
.output.txt
