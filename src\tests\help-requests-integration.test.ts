/**
 * 资源求助功能集成测试
 * 测试各个组件和页面的集成
 */

import { describe, it, expect, beforeEach } from '@jest/globals';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import {
  HelpRequest,
  HelpAnswer,
  PAN_TYPE_MAP,
  RESOURCE_TYPES,
} from '@/types/help-request';
import { DEFAULT_USER_LEVELS } from '@/types/user-level';

// Mock数据
const mockHelpRequest: HelpRequest = {
  id: 1,
  title: '寻找某个视频资源',
  description: '这是一个测试求助的详细描述',
  resource_types: ['video'],
  pan_types: [1, 2],
  status: 'open',
  user_id: 1,
  user: {
    id: 1,
    username: 'testuser',
    nickname: '测试用户',
    level: 2,
    title: '初学者',
  },
  answers_count: 3,
  best_answer_id: undefined,
  created_at: '2024-01-01T00:00:00Z',
  updated_at: '2024-01-01T00:00:00Z',
  view_count: 15,
  tags: ['测试', '视频'],
};

const mockAnswer: HelpAnswer = {
  id: 1,
  help_request_id: 1,
  user_id: 2,
  user: {
    id: 2,
    username: 'helper',
    nickname: '热心用户',
    level: 3,
    title: '活跃用户',
  },
  resource_link: 'https://pan.baidu.com/s/test123',
  pan_type: 1,
  description: '这是资源的详细描述和提取码',
  is_parsed: false,
  is_best: false,
  created_at: '2024-01-01T01:00:00Z',
  updated_at: '2024-01-01T01:00:00Z',
};

describe('资源求助功能集成测试', () => {
  beforeEach(() => {
    // 重置所有mock
    jest.clearAllMocks();
  });

  describe('数据验证', () => {
    it('应该正确验证求助数据完整性', () => {
      expect(mockHelpRequest.id).toBeDefined();
      expect(mockHelpRequest.title).toBeTruthy();
      expect(mockHelpRequest.pan_types.length).toBeGreaterThan(0);
      expect(mockHelpRequest.user).toBeDefined();
      expect(mockHelpRequest.status).toMatch(/^(open|solved|closed)$/);
    });

    it('应该正确验证回答数据完整性', () => {
      expect(mockAnswer.id).toBeDefined();
      expect(mockAnswer.help_request_id).toBe(mockHelpRequest.id);
      expect(mockAnswer.resource_link).toMatch(/^https?:\/\//);
      expect(mockAnswer.pan_type).toBeGreaterThan(0);
      expect(mockAnswer.user).toBeDefined();
    });

    it('应该正确映射网盘类型', () => {
      mockHelpRequest.pan_types.forEach(panType => {
        expect(PAN_TYPE_MAP[panType as keyof typeof PAN_TYPE_MAP]).toBeDefined();
      });
      expect(PAN_TYPE_MAP[mockAnswer.pan_type as keyof typeof PAN_TYPE_MAP]).toBeDefined();
    });

    it('应该正确映射资源类型', () => {
      if (mockHelpRequest.resource_types) {
        mockHelpRequest.resource_types.forEach(resourceType => {
          const found = RESOURCE_TYPES.find(rt => rt.value === resourceType);
          expect(found).toBeDefined();
        });
      }
    });
  });

  describe('用户等级系统', () => {
    it('应该正确计算用户等级', () => {
      const getUserLevel = (level: number) => {
        return DEFAULT_USER_LEVELS.find(l => 
          level >= l.min_points && level <= l.max_points
        ) || DEFAULT_USER_LEVELS[0];
      };

      // 测试不同等级
      expect(getUserLevel(0).title).toBe('新手');
      expect(getUserLevel(150).title).toBe('初学者');
      expect(getUserLevel(500).title).toBe('活跃用户');
      expect(getUserLevel(1000).title).toBe('资深用户');
      expect(getUserLevel(2000).title).toBe('专家');
      expect(getUserLevel(5000).title).toBe('大师');
    });

    it('应该正确显示用户头衔', () => {
      expect(mockHelpRequest.user.level).toBe(2);
      expect(mockAnswer.user.level).toBe(3);
      
      // 验证等级对应的头衔
      const level2 = DEFAULT_USER_LEVELS.find(l => l.level === 2);
      const level3 = DEFAULT_USER_LEVELS.find(l => l.level === 3);
      
      expect(level2?.title).toBe('初学者');
      expect(level3?.title).toBe('活跃用户');
    });
  });

  describe('权限控制逻辑', () => {
    it('应该正确检查查看权限', () => {
      // 所有人都可以查看
      const canView = true;
      expect(canView).toBe(true);
    });

    it('应该正确检查发布权限', () => {
      // 需要登录才能发布
      const isAuthenticated = true;
      const canCreate = isAuthenticated;
      expect(canCreate).toBe(true);

      const isGuest = false;
      const guestCanCreate = isGuest;
      expect(guestCanCreate).toBe(false);
    });

    it('应该正确检查采纳权限', () => {
      // 只有求助者可以采纳答案
      const currentUserId = mockHelpRequest.user_id;
      const canAdopt = currentUserId === mockHelpRequest.user_id && mockHelpRequest.status === 'open';
      expect(canAdopt).toBe(true);

      const otherUserId = 999;
      const otherCanAdopt = otherUserId === mockHelpRequest.user_id;
      expect(otherCanAdopt).toBe(false);
    });

    it('应该正确检查删除权限', () => {
      // 求助者或管理员可以删除
      const ownerId = mockHelpRequest.user_id;
      const ownerCanDelete = ownerId === mockHelpRequest.user_id;
      expect(ownerCanDelete).toBe(true);

      const isAdmin = true;
      const adminCanDelete = isAdmin;
      expect(adminCanDelete).toBe(true);

      const otherUserId = 999;
      const isNotAdmin = false;
      const otherCanDelete = otherUserId === mockHelpRequest.user_id || isNotAdmin;
      expect(otherCanDelete).toBe(false);
    });
  });

  describe('状态管理', () => {
    it('应该正确处理求助状态变化', () => {
      // 初始状态
      expect(mockHelpRequest.status).toBe('open');

      // 模拟状态变化
      const updatedRequest = { ...mockHelpRequest, status: 'solved' as const };
      expect(updatedRequest.status).toBe('solved');

      // 验证状态有效性
      const validStatuses = ['open', 'solved', 'closed'];
      expect(validStatuses).toContain(updatedRequest.status);
    });

    it('应该正确处理最佳答案设置', () => {
      // 初始没有最佳答案
      expect(mockHelpRequest.best_answer_id).toBeUndefined();

      // 设置最佳答案
      const updatedRequest = { 
        ...mockHelpRequest, 
        best_answer_id: mockAnswer.id,
        status: 'solved' as const
      };
      
      expect(updatedRequest.best_answer_id).toBe(mockAnswer.id);
      expect(updatedRequest.status).toBe('solved');

      // 更新答案状态
      const updatedAnswer = { ...mockAnswer, is_best: true };
      expect(updatedAnswer.is_best).toBe(true);
    });
  });

  describe('数据格式化', () => {
    it('应该正确格式化日期', () => {
      const formatDate = (dateString: string) => {
        const date = new Date(dateString);
        const now = new Date();
        const diffTime = Math.abs(now.getTime() - date.getTime());
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

        if (diffDays === 1) {
          return '今天';
        } else if (diffDays <= 7) {
          return `${diffDays}天前`;
        } else {
          return date.toLocaleDateString('zh-CN');
        }
      };

      // 测试日期格式化
      const testDate = '2024-01-01T00:00:00Z';
      const formatted = formatDate(testDate);
      expect(typeof formatted).toBe('string');
      expect(formatted.length).toBeGreaterThan(0);
    });

    it('应该正确格式化网盘类型显示', () => {
      const formatPanTypes = (panTypes: number[]) => {
        return panTypes.map(type => PAN_TYPE_MAP[type as keyof typeof PAN_TYPE_MAP]).join('、');
      };

      const formatted = formatPanTypes(mockHelpRequest.pan_types);
      expect(formatted).toContain('百度网盘');
      expect(formatted).toContain('夸克网盘');
    });

    it('应该正确处理资源类型显示', () => {
      const formatResourceTypes = (resourceTypes?: string[]) => {
        if (!resourceTypes) return [];
        return resourceTypes.map(type => {
          const found = RESOURCE_TYPES.find(rt => rt.value === type);
          return found ? found.label : type;
        });
      };

      const formatted = formatResourceTypes(mockHelpRequest.resource_types);
      expect(formatted).toContain('视频');
    });
  });

  describe('搜索和筛选', () => {
    it('应该正确筛选求助状态', () => {
      const helpRequests = [
        { ...mockHelpRequest, status: 'open' as const },
        { ...mockHelpRequest, id: 2, status: 'solved' as const },
        { ...mockHelpRequest, id: 3, status: 'closed' as const },
      ];

      const openRequests = helpRequests.filter(req => req.status === 'open');
      const solvedRequests = helpRequests.filter(req => req.status === 'solved');
      const closedRequests = helpRequests.filter(req => req.status === 'closed');

      expect(openRequests).toHaveLength(1);
      expect(solvedRequests).toHaveLength(1);
      expect(closedRequests).toHaveLength(1);
    });

    it('应该正确筛选网盘类型', () => {
      const helpRequests = [
        { ...mockHelpRequest, pan_types: [1] },
        { ...mockHelpRequest, id: 2, pan_types: [2] },
        { ...mockHelpRequest, id: 3, pan_types: [1, 2] },
      ];

      const baiduRequests = helpRequests.filter(req => req.pan_types.includes(1));
      const quarkRequests = helpRequests.filter(req => req.pan_types.includes(2));

      expect(baiduRequests).toHaveLength(2);
      expect(quarkRequests).toHaveLength(2);
    });

    it('应该正确搜索求助标题', () => {
      const helpRequests = [
        { ...mockHelpRequest, title: '寻找电影资源' },
        { ...mockHelpRequest, id: 2, title: '需要音乐专辑' },
        { ...mockHelpRequest, id: 3, title: '寻找软件安装包' },
      ];

      const searchKeyword = '寻找';
      const searchResults = helpRequests.filter(req => 
        req.title.includes(searchKeyword)
      );

      expect(searchResults).toHaveLength(2);
    });
  });

  describe('错误处理', () => {
    it('应该正确处理空数据', () => {
      const emptyRequests: HelpRequest[] = [];
      expect(emptyRequests).toHaveLength(0);

      const emptyAnswers: HelpAnswer[] = [];
      expect(emptyAnswers).toHaveLength(0);
    });

    it('应该正确处理无效的网盘类型', () => {
      const invalidPanType = 999;
      const panTypeName = PAN_TYPE_MAP[invalidPanType as keyof typeof PAN_TYPE_MAP];
      expect(panTypeName).toBeUndefined();
    });

    it('应该正确处理无效的资源类型', () => {
      const invalidResourceType = 'invalid_type';
      const found = RESOURCE_TYPES.find(rt => rt.value === invalidResourceType);
      expect(found).toBeUndefined();
    });
  });
});

// 性能测试
describe('性能测试', () => {
  it('应该能够处理大量求助数据', () => {
    const largeDataSet = Array.from({ length: 1000 }, (_, index) => ({
      ...mockHelpRequest,
      id: index + 1,
      title: `求助 ${index + 1}`,
    }));

    expect(largeDataSet).toHaveLength(1000);

    // 测试筛选性能
    const start = performance.now();
    const filtered = largeDataSet.filter(req => req.status === 'open');
    const end = performance.now();

    expect(filtered).toHaveLength(1000);
    expect(end - start).toBeLessThan(100); // 应该在100ms内完成
  });

  it('应该能够快速搜索求助', () => {
    const largeDataSet = Array.from({ length: 1000 }, (_, index) => ({
      ...mockHelpRequest,
      id: index + 1,
      title: `求助 ${index + 1}`,
    }));

    const searchKeyword = '求助 500';
    const start = performance.now();
    const searchResults = largeDataSet.filter(req => 
      req.title.includes(searchKeyword)
    );
    const end = performance.now();

    expect(searchResults).toHaveLength(1);
    expect(end - start).toBeLessThan(50); // 应该在50ms内完成
  });
});
