"use client";

import { useState, useEffect, useCallback } from "react";
import Link from "next/link";
import { PlusIcon, MagnifyingGlassIcon } from "@heroicons/react/24/outline";
import { useAuth } from "@/hooks/useAuth";
import { useToast } from "@/components/ToastProvider";
import { getHelpRequests } from "@/services/helpRequestService";
import {
  type HelpRequest,
  type HelpRequestFilters,
  PAN_TYPE_MAP,
  RESOURCE_TYPES,
} from "@/types/help-request";
import Pagination from "@/components/Pagination";

// 求助卡片组件
function HelpRequestCard({ helpRequest }: { helpRequest: HelpRequest }) {
  const getStatusColor = (status: string) => {
    switch (status) {
      case "open":
        return "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400";
      case "solved":
        return "bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400";
      case "closed":
        return "bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400";
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400";
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case "open":
        return "求助中";
      case "solved":
        return "已解决";
      case "closed":
        return "已关闭";
      default:
        return "未知";
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffTime = Math.abs(now.getTime() - date.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays === 1) {
      return "今天";
    } else if (diffDays <= 7) {
      return `${diffDays}天前`;
    } else {
      return date.toLocaleDateString("zh-CN");
    }
  };

  return (
    <div className="bg-card-background border border-border-color rounded-lg p-6 hover:shadow-md transition-shadow">
      <div className="flex items-start justify-between mb-3">
        <Link
          href={`/help-requests/${helpRequest.id}`}
          className="text-lg font-semibold text-gray-900 dark:text-gray-100 hover:text-blue-600 transition-colors line-clamp-2"
        >
          {helpRequest.title}
        </Link>
        <span
          className={`px-2 py-1 rounded-full text-xs font-medium whitespace-nowrap ml-3 ${getStatusColor(
            helpRequest.status
          )}`}
        >
          {getStatusText(helpRequest.status)}
        </span>
      </div>

      {helpRequest.description && (
        <p className="text-secondary-text text-sm mb-3 line-clamp-2">
          {helpRequest.description}
        </p>
      )}

      <div className="flex flex-wrap gap-2 mb-3">
        {helpRequest.pan_types.map((panType) => (
          <span
            key={panType}
            className="px-2 py-1 bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400 rounded text-xs"
          >
            {PAN_TYPE_MAP[panType as keyof typeof PAN_TYPE_MAP]}
          </span>
        ))}
        {helpRequest.resource_types?.map((resourceType) => (
          <span
            key={resourceType}
            className="px-2 py-1 bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-400 rounded text-xs"
          >
            {RESOURCE_TYPES.find((rt) => rt.value === resourceType)?.label ||
              resourceType}
          </span>
        ))}
      </div>

      <div className="flex items-center justify-between text-sm text-secondary-text">
        <div className="flex items-center space-x-4">
          <span>
            发布者:{" "}
            <span className="font-medium">{helpRequest.user.username}</span>
          </span>
          <span>{helpRequest.answers_count} 个回答</span>
          <span>{helpRequest.view_count} 次浏览</span>
        </div>
        <span>{formatDate(helpRequest.created_at)}</span>
      </div>
    </div>
  );
}

// 筛选器组件
function HelpRequestFilters({
  filters,
  onFiltersChange,
}: {
  filters: HelpRequestFilters;
  onFiltersChange: (filters: HelpRequestFilters) => void;
}) {
  const [searchQuery, setSearchQuery] = useState("");

  const handleFilterChange = (key: keyof HelpRequestFilters, value: any) => {
    onFiltersChange({
      ...filters,
      [key]: value,
    });
  };

  const handleSearch = () => {
    // 这里可以添加搜索逻辑
    console.log("搜索:", searchQuery);
  };

  return (
    <div className="bg-card-background border border-border-color rounded-lg p-4 mb-6">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
        {/* 状态筛选 */}
        <div>
          <label className="block text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">
            状态
          </label>
          <select
            value={filters.status || "all"}
            onChange={(e) => handleFilterChange("status", e.target.value)}
            className="w-full px-3 py-2 border border-border-color rounded-md bg-background text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500"
            title="选择状态筛选条件"
          >
            <option value="all">全部状态</option>
            <option value="open">求助中</option>
            <option value="solved">已解决</option>
            <option value="closed">已关闭</option>
          </select>
        </div>

        {/* 网盘类型筛选 */}
        <div>
          <label className="block text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">
            网盘类型
          </label>
          <select
            value={filters.pan_type || ""}
            onChange={(e) =>
              handleFilterChange(
                "pan_type",
                e.target.value ? parseInt(e.target.value) : undefined
              )
            }
            className="w-full px-3 py-2 border border-border-color rounded-md bg-background text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500"
            title="选择网盘类型筛选条件"
          >
            <option value="">全部网盘</option>
            {Object.entries(PAN_TYPE_MAP).map(([key, value]) => (
              <option key={key} value={key}>
                {value}
              </option>
            ))}
          </select>
        </div>

        {/* 排序方式 */}
        <div>
          <label className="block text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">
            排序方式
          </label>
          <select
            value={filters.sort_by || "latest"}
            onChange={(e) => handleFilterChange("sort_by", e.target.value)}
            className="w-full px-3 py-2 border border-border-color rounded-md bg-background text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500"
            title="选择排序方式"
          >
            <option value="latest">最新发布</option>
            <option value="oldest">最早发布</option>
            <option value="most_answers">回答最多</option>
            <option value="most_viewed">浏览最多</option>
            <option value="unsolved">未解决</option>
          </select>
        </div>

        {/* 时间范围 */}
        <div>
          <label className="block text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">
            时间范围
          </label>
          <select
            value={filters.time_range || "all"}
            onChange={(e) => handleFilterChange("time_range", e.target.value)}
            className="w-full px-3 py-2 border border-border-color rounded-md bg-background text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500"
            title="选择时间范围筛选条件"
          >
            <option value="all">全部时间</option>
            <option value="today">今天</option>
            <option value="week">本周</option>
            <option value="month">本月</option>
            <option value="year">今年</option>
          </select>
        </div>
      </div>

      {/* 搜索框 */}
      <div className="flex gap-2">
        <div className="flex-1 relative">
          <input
            type="text"
            placeholder="搜索求助标题或描述..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            onKeyPress={(e) => e.key === "Enter" && handleSearch()}
            className="w-full px-3 py-2 pl-10 border border-border-color rounded-md bg-background text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
          <MagnifyingGlassIcon className="h-5 w-5 text-secondary-text absolute left-3 top-1/2 transform -translate-y-1/2" />
        </div>
        <button
          type="button"
          onClick={handleSearch}
          className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
        >
          搜索
        </button>
      </div>
    </div>
  );
}

export default function HelpRequestsPage() {
  const { isAuthenticated } = useAuth();
  const { showToast } = useToast();
  const [helpRequests, setHelpRequests] = useState<HelpRequest[]>([]);
  const [loading, setLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [total, setTotal] = useState(0);
  const [filters, setFilters] = useState<HelpRequestFilters>({
    status: "all",
    sort_by: "latest",
    time_range: "all",
  });

  const pageSize = 20;

  const loadHelpRequests = useCallback(async () => {
    setLoading(true);
    try {
      const response = await getHelpRequests(currentPage, pageSize, filters);
      if (response.success) {
        setHelpRequests(response.data.help_requests);
        setTotalPages(response.data.total_pages);
        setTotal(response.data.total);
      } else {
        showToast(response.error || "获取求助列表失败", "error");
      }
    } catch {
      showToast("获取求助列表失败", "error");
    } finally {
      setLoading(false);
    }
  }, [currentPage, filters, showToast]);

  useEffect(() => {
    loadHelpRequests();
  }, [loadHelpRequests]);

  const handleFiltersChange = (newFilters: HelpRequestFilters) => {
    setFilters(newFilters);
    setCurrentPage(1); // 重置到第一页
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    window.scrollTo(0, 0);
  };

  return (
    <div>
      {/* 页面头部 */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
            资源求助
          </h1>
          <p className="text-secondary-text mt-1">
            向社区求助，找到您需要的网盘资源
          </p>
        </div>
        {isAuthenticated && (
          <Link
            href="/help-requests/create"
            className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            <PlusIcon className="h-5 w-5 mr-2" />
            发布求助
          </Link>
        )}
      </div>

      {/* 筛选器 */}
      <HelpRequestFilters
        filters={filters}
        onFiltersChange={handleFiltersChange}
      />

      {/* 统计信息 */}
      {!loading && (
        <div className="mb-4 text-sm text-secondary-text">
          共找到{" "}
          <span className="font-semibold text-gray-900 dark:text-gray-100">
            {total}
          </span>{" "}
          个求助
        </div>
      )}

      {/* 求助列表 */}
      {loading ? (
        <div className="flex justify-center items-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <span className="ml-2 text-secondary-text">加载中...</span>
        </div>
      ) : helpRequests.length > 0 ? (
        <div className="space-y-4">
          {helpRequests.map((helpRequest) => (
            <HelpRequestCard key={helpRequest.id} helpRequest={helpRequest} />
          ))}
        </div>
      ) : (
        <div className="text-center py-12">
          <p className="text-secondary-text">暂无求助信息</p>
          {isAuthenticated && (
            <Link
              href="/help-requests/create"
              className="inline-flex items-center px-4 py-2 mt-4 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              <PlusIcon className="h-5 w-5 mr-2" />
              发布第一个求助
            </Link>
          )}
        </div>
      )}

      {/* 分页 */}
      {!loading && totalPages > 1 && (
        <div className="mt-8">
          <Pagination
            currentPage={currentPage}
            totalPages={totalPages}
            onPageChange={handlePageChange}
          />
        </div>
      )}
    </div>
  );
}
