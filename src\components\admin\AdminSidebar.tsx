"use client";

import Link from "next/link";
import { usePathname } from "next/navigation";
import { useAdminSidebarContext } from "@/contexts/AdminContext";
import {
  HomeIcon,
  UsersIcon,
  FolderIcon,
  ChatBubbleLeftRightIcon,
  WrenchScrewdriverIcon,
  XMarkIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
} from "@heroicons/react/24/outline";

// eslint-disable-next-line @typescript-eslint/no-empty-object-type
interface AdminSidebarProps {
  // 组件现在通过Context获取状态，不需要props
}

const navigation = [
  {
    name: "仪表盘",
    href: "/admin",
    icon: HomeIcon,
    description: "系统概览和统计信息",
  },
  {
    name: "用户管理",
    href: "/admin/users",
    icon: UsersIcon,
    description: "管理用户账户和权限",
  },
  {
    name: "资源管理",
    href: "/admin/resources",
    icon: FolderIcon,
    description: "处理用户上传的资源",
  },
  {
    name: "反馈管理",
    href: "/admin/feedback",
    icon: ChatBubbleLeftRightIcon,
    description: "处理用户反馈信息",
  },
  {
    name: "配置管理",
    href: "/admin/config",
    icon: WrenchScrewdriverIcon,
    description: "高级配置管理和系统参数",
  },
];

export default function AdminSidebar({}: AdminSidebarProps) {
  const pathname = usePathname();
  const { isOpen, isCollapsed, isMobile, toggleCollapse, closeSidebar } =
    useAdminSidebarContext();

  const sidebarContent = (
    <div className="flex flex-col h-full">
      {/* 移动端顶部关闭按钮 - 移除标题 */}
      {isMobile && (
        <div className="flex items-center justify-end p-4 border-b border-border-color">
          <button
            type="button"
            onClick={closeSidebar}
            className="p-2 rounded-lg hover:bg-hover-background transition-colors"
            aria-label="关闭菜单"
          >
            <XMarkIcon className="h-5 w-5 text-foreground" />
          </button>
        </div>
      )}

      {/* 桌面端顶部区域 - 移除标题，保留边框 */}
      {!isMobile && <div className="h-4 border-b border-border-color"></div>}

      {/* 导航菜单 */}
      <nav
        className={`flex-1 space-y-1 ${isMobile ? "pt-2 px-2" : "pt-4"} ${
          isCollapsed && !isMobile ? "px-1" : "px-2"
        }`}
      >
        {navigation.map((item) => {
          const isActive = pathname === item.href;

          if (isCollapsed && !isMobile) {
            // 折叠模式：只显示图标
            return (
              <Link
                key={item.name}
                href={item.href}
                className={`group flex items-center justify-center p-3 rounded-md transition-colors relative ${
                  isActive
                    ? "bg-gradient-to-r from-blue-600 to-purple-600 text-white"
                    : "text-foreground hover:bg-hover-background"
                }`}
                title={item.name}
                onClick={() => isMobile && closeSidebar()}
              >
                <item.icon
                  className={`h-5 w-5 ${
                    isActive ? "text-white" : "text-secondary-text"
                  }`}
                />
                {/* Tooltip */}
                <div className="absolute left-full ml-2 px-2 py-1 bg-gray-900 text-white text-sm rounded opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none whitespace-nowrap z-50">
                  {item.name}
                </div>
              </Link>
            );
          }

          // 展开模式：显示图标和文字
          return (
            <Link
              key={item.name}
              href={item.href}
              className={`group flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors ${
                isActive
                  ? "bg-gradient-to-r from-blue-600 to-purple-600 text-white"
                  : "text-foreground hover:bg-hover-background"
              }`}
              onClick={() => isMobile && closeSidebar()}
            >
              <item.icon
                className={`flex-shrink-0 h-5 w-5 mr-3 ${
                  isActive ? "text-white" : "text-secondary-text"
                }`}
              />
              <div className="flex-1 min-w-0">
                <div className="font-medium truncate">{item.name}</div>
                <div
                  className={`text-xs truncate ${
                    isActive
                      ? "text-blue-100"
                      : "text-secondary-text group-hover:text-foreground"
                  }`}
                >
                  {item.description}
                </div>
              </div>
            </Link>
          );
        })}
      </nav>

      {/* 底部区域 */}
      <div className="border-t border-border-color">
        {/* 桌面端折叠按钮 - 移动到底部 */}
        {!isMobile && (
          <div className="p-2 border-b border-border-color">
            <button
              type="button"
              onClick={(e) => {
                // 阻止事件冒泡，确保不会触发其他事件
                e.preventDefault();
                e.stopPropagation();
                console.log("🔄 AdminSidebar: 底部折叠按钮被点击", {
                  isCollapsed,
                });
                toggleCollapse();
              }}
              className="w-full p-2 rounded-lg hover:bg-hover-background transition-colors flex items-center justify-center"
              aria-label={isCollapsed ? "展开侧边栏" : "折叠侧边栏"}
              title={isCollapsed ? "展开侧边栏" : "折叠侧边栏"}
            >
              {isCollapsed ? (
                <ChevronRightIcon className="h-4 w-4 text-foreground" />
              ) : (
                <div className="flex items-center space-x-2">
                  <ChevronLeftIcon className="h-4 w-4 text-foreground" />
                  <span className="text-xs text-secondary-text">收起</span>
                </div>
              )}
            </button>
          </div>
        )}

        {/* 版本信息 - 移除"管理后台"文字 */}
        {!isCollapsed || isMobile ? (
          <div className="p-4">
            <div className="text-xs text-secondary-text text-center">v1.0</div>
          </div>
        ) : (
          <div className="p-2">
            <div
              className="text-xs text-secondary-text text-center"
              title="v1.0"
            >
              v1.0
            </div>
          </div>
        )}
      </div>
    </div>
  );

  if (isMobile) {
    console.log("🎨 AdminSidebar: 移动端侧边栏渲染", {
      isOpen,
      isMobile,
      backgroundFix: "admin-sidebar-mobile-solid",
    });

    return (
      <>
        {/* 移动端侧边栏 - 修复背景透明问题 */}
        <div
          className={`admin-sidebar admin-sidebar-mobile-solid fixed left-0 top-12 md:top-14 bottom-0 z-[50] w-64 bg-card-background border-r border-border-color transform transition-transform duration-300 shadow-xl ${
            isOpen ? "translate-x-0" : "-translate-x-full"
          }`}
        >
          {sidebarContent}
          {/* 在侧边栏内部添加关闭按钮 */}
          {isOpen && (
            <div className="absolute top-4 right-4">
              <button
                type="button"
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();

                  closeSidebar();
                }}
                className="p-2 rounded-lg hover:bg-hover-background transition-colors"
                aria-label="关闭侧边栏"
              >
                <XMarkIcon className="h-5 w-5 text-foreground" />
              </button>
            </div>
          )}
        </div>
      </>
    );
  }

  // 桌面端布局
  return (
    <div
      className={`bg-card-background border-r border-border-color min-h-full transition-all duration-300 ${
        isOpen ? (isCollapsed ? "w-16" : "w-64") : "w-0 overflow-hidden"
      }`}
    >
      {isOpen && sidebarContent}
    </div>
  );
}
