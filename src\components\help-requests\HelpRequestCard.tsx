/**
 * 求助卡片组件
 * 用于在列表页面展示求助信息
 */

import Link from "next/link";
import { EyeIcon, ChatBubbleLeftRightIcon } from "@heroicons/react/24/outline";
import { CheckCircleIcon } from "@heroicons/react/24/solid";
import {
  HelpRequest,
  PAN_TYPE_MAP,
  RESOURCE_TYPES,
} from "@/types/help-request";

interface HelpRequestCardProps {
  helpRequest: HelpRequest;
  showUser?: boolean; // 是否显示用户信息
  className?: string;
}

export default function HelpRequestCard({
  helpRequest,
  showUser = true,
  className = "",
}: HelpRequestCardProps) {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'open':
        return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400';
      case 'solved':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400';
      case 'closed':
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'open':
        return '求助中';
      case 'solved':
        return '已解决';
      case 'closed':
        return '已关闭';
      default:
        return '未知';
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffTime = Math.abs(now.getTime() - date.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays === 1) {
      return '今天';
    } else if (diffDays <= 7) {
      return `${diffDays}天前`;
    } else {
      return date.toLocaleDateString('zh-CN');
    }
  };

  return (
    <div className={`bg-card-background border border-border-color rounded-lg p-6 hover:shadow-md transition-shadow ${className}`}>
      <div className="flex items-start justify-between mb-3">
        <Link
          href={`/help-requests/${helpRequest.id}`}
          className="text-lg font-semibold text-foreground hover:text-blue-600 transition-colors line-clamp-2 flex-1"
        >
          {helpRequest.title}
        </Link>
        <div className="flex items-center space-x-2 ml-3">
          {helpRequest.status === 'solved' && (
            <CheckCircleIcon className="h-5 w-5 text-green-600" title="已解决" />
          )}
          <span
            className={`px-2 py-1 rounded-full text-xs font-medium whitespace-nowrap ${getStatusColor(
              helpRequest.status
            )}`}
          >
            {getStatusText(helpRequest.status)}
          </span>
        </div>
      </div>

      {helpRequest.description && (
        <p className="text-secondary-text text-sm mb-3 line-clamp-2">
          {helpRequest.description}
        </p>
      )}

      {/* 标签区域 */}
      <div className="flex flex-wrap gap-2 mb-3">
        {/* 网盘类型标签 */}
        {helpRequest.pan_types.slice(0, 3).map((panType) => (
          <span
            key={panType}
            className="px-2 py-1 bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400 rounded text-xs"
          >
            {PAN_TYPE_MAP[panType as keyof typeof PAN_TYPE_MAP]}
          </span>
        ))}
        {helpRequest.pan_types.length > 3 && (
          <span className="px-2 py-1 bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400 rounded text-xs">
            +{helpRequest.pan_types.length - 3}
          </span>
        )}
        
        {/* 资源类型标签 */}
        {helpRequest.resource_types?.slice(0, 2).map((resourceType) => (
          <span
            key={resourceType}
            className="px-2 py-1 bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-400 rounded text-xs"
          >
            {RESOURCE_TYPES.find(rt => rt.value === resourceType)?.label || resourceType}
          </span>
        ))}
        {(helpRequest.resource_types?.length || 0) > 2 && (
          <span className="px-2 py-1 bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400 rounded text-xs">
            +{(helpRequest.resource_types?.length || 0) - 2}
          </span>
        )}
      </div>

      {/* 底部信息 */}
      <div className="flex items-center justify-between text-sm text-secondary-text">
        <div className="flex items-center space-x-4">
          {showUser && (
            <div className="flex items-center">
              <div className="w-6 h-6 bg-blue-100 dark:bg-blue-900/20 rounded-full flex items-center justify-center mr-2">
                <span className="text-blue-600 text-xs font-medium">
                  {helpRequest.user.username.charAt(0).toUpperCase()}
                </span>
              </div>
              <span className="font-medium">{helpRequest.user.username}</span>
              {helpRequest.user.level > 1 && (
                <span className="ml-1 text-xs text-blue-600">
                  Lv.{helpRequest.user.level}
                </span>
              )}
            </div>
          )}
          <div className="flex items-center">
            <ChatBubbleLeftRightIcon className="h-4 w-4 mr-1" />
            <span>{helpRequest.answers_count} 个回答</span>
          </div>
          <div className="flex items-center">
            <EyeIcon className="h-4 w-4 mr-1" />
            <span>{helpRequest.view_count} 次浏览</span>
          </div>
        </div>
        <span>{formatDate(helpRequest.created_at)}</span>
      </div>
    </div>
  );
}
