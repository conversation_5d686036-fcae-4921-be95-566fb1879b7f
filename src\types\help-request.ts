/**
 * 资源求助相关的类型定义
 */

// 求助状态枚举
export type HelpRequestStatus = 'open' | 'solved' | 'closed';

// 网盘类型映射
export const PAN_TYPE_MAP = {
  1: '百度网盘',
  2: '夸克网盘', 
  3: '阿里云盘',
  4: '迅雷网盘',
} as const;

// 资源类型选项
export const RESOURCE_TYPES = [
  { value: 'video', label: '视频' },
  { value: 'audio', label: '音频' },
  { value: 'image', label: '图片' },
  { value: 'document', label: '文档' },
  { value: 'archive', label: '压缩包' },
  { value: 'application', label: '应用软件' },
  { value: 'game', label: '游戏' },
  { value: 'ebook', label: '电子书' },
  { value: 'other', label: '其他' },
] as const;

// 用户基础信息
export interface UserBasicInfo {
  id: number;
  username: string;
  nickname?: string;
  level: number;
  title?: string;
  avatar?: string;
}

// 求助主体
export interface HelpRequest {
  id: number;
  title: string;                    // 资源名称（必填）
  description?: string;             // 资源描述（可选）
  resource_types?: string[];        // 资源类型（可选）
  pan_types: number[];             // 网盘类型（必填，支持多选）
  status: HelpRequestStatus;
  user_id: number;
  user: UserBasicInfo;
  answers_count: number;
  best_answer_id?: number;
  created_at: string;
  updated_at: string;
  tags?: string[];                  // 标签
  view_count: number;               // 浏览次数
}

// 求助回答
export interface HelpAnswer {
  id: number;
  help_request_id: number;
  user_id: number;
  user: UserBasicInfo;
  resource_link: string;            // 资源链接（必填）
  pan_type: number;                 // 网盘类型（必填）
  description?: string;             // 资源描述（可选）
  is_parsed: boolean;               // 是否解析
  is_best: boolean;                 // 是否为最佳答案
  created_at: string;
  updated_at: string;
}

// 求助筛选条件
export interface HelpRequestFilters {
  status?: 'all' | HelpRequestStatus;
  pan_type?: number;
  resource_type?: string;
  sort_by?: 'latest' | 'oldest' | 'most_answers' | 'unsolved' | 'most_viewed';
  time_range?: 'all' | 'today' | 'week' | 'month' | 'year';
  user_id?: number;                 // 用于筛选特定用户的求助
}

// 创建求助的数据
export interface CreateHelpRequestData {
  title: string;
  description?: string;
  resource_types?: string[];
  pan_types: number[];
  tags?: string[];
}

// 创建回答的数据
export interface CreateAnswerData {
  help_request_id: number;
  resource_link: string;
  pan_type: number;
  description?: string;
  is_parsed: boolean;
}

// API响应类型
export interface HelpRequestListResponse {
  success: boolean;
  data: {
    help_requests: HelpRequest[];
    total: number;
    page: number;
    limit: number;
    total_pages: number;
  };
  message?: string;
  error?: string;
}

export interface HelpRequestDetailResponse {
  success: boolean;
  data: {
    help_request: HelpRequest;
    answers: HelpAnswer[];
  };
  message?: string;
  error?: string;
}

export interface ApiResponse {
  success: boolean;
  message: string;
  data?: any;
  error?: string;
}

// 管理员相关类型
export interface AdminHelpRequestFilters extends HelpRequestFilters {
  search?: string;                  // 搜索关键词
}

export interface AdminHelpRequestListResponse {
  success: boolean;
  data: {
    help_requests: (HelpRequest & {
      user_email?: string;          // 管理员可以看到用户邮箱
    })[];
    total: number;
    page: number;
    limit: number;
    total_pages: number;
  };
  message?: string;
  error?: string;
}

// 求助统计信息
export interface HelpRequestStats {
  total_requests: number;
  open_requests: number;
  solved_requests: number;
  closed_requests: number;
  total_answers: number;
  today_requests: number;
  week_requests: number;
}
