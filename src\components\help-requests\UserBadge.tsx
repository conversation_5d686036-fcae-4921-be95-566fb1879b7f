/**
 * 用户头衔/等级组件
 * 显示用户的等级、头衔和徽章
 * 注意：此组件使用内联样式来显示动态的用户等级颜色，这是必要的功能
 */

import { UserBasicInfo } from "@/types/help-request";
import { UserLevel, DEFAULT_USER_LEVELS } from "@/types/user-level";

interface UserBadgeProps {
  user: UserBasicInfo;
  showLevel?: boolean;
  showTitle?: boolean;
  size?: "sm" | "md" | "lg";
  className?: string;
}

export default function UserBadge({
  user,
  showLevel = true,
  showTitle = true,
  size = "md",
  className = "",
}: UserBadgeProps) {
  // 根据用户等级获取等级信息
  const getUserLevel = (level: number): UserLevel => {
    return (
      DEFAULT_USER_LEVELS.find(
        (l) => level >= l.min_points && level <= l.max_points
      ) || DEFAULT_USER_LEVELS[0]
    );
  };

  const userLevel = getUserLevel(user.level);

  const sizeClasses = {
    sm: {
      avatar: "w-6 h-6 text-xs",
      text: "text-xs",
      badge: "text-xs px-1.5 py-0.5",
    },
    md: {
      avatar: "w-8 h-8 text-sm",
      text: "text-sm",
      badge: "text-xs px-2 py-1",
    },
    lg: {
      avatar: "w-10 h-10 text-base",
      text: "text-base",
      badge: "text-sm px-2 py-1",
    },
  };

  const classes = sizeClasses[size];

  // 获取等级颜色的CSS类
  const getLevelColorClass = (color: string) => {
    const colorMap: Record<string, string> = {
      "#9CA3AF":
        "bg-gray-100 text-gray-600 dark:bg-gray-900/20 dark:text-gray-400",
      "#10B981":
        "bg-green-100 text-green-600 dark:bg-green-900/20 dark:text-green-400",
      "#3B82F6":
        "bg-blue-100 text-blue-600 dark:bg-blue-900/20 dark:text-blue-400",
      "#8B5CF6":
        "bg-purple-100 text-purple-600 dark:bg-purple-900/20 dark:text-purple-400",
      "#F59E0B":
        "bg-yellow-100 text-yellow-600 dark:bg-yellow-900/20 dark:text-yellow-400",
      "#EF4444": "bg-red-100 text-red-600 dark:bg-red-900/20 dark:text-red-400",
    };
    return (
      colorMap[color] ||
      "bg-gray-100 text-gray-600 dark:bg-gray-900/20 dark:text-gray-400"
    );
  };

  const getBadgeColorClass = (color: string) => {
    const colorMap: Record<string, string> = {
      "#9CA3AF": "bg-gray-500",
      "#10B981": "bg-green-500",
      "#3B82F6": "bg-blue-500",
      "#8B5CF6": "bg-purple-500",
      "#F59E0B": "bg-yellow-500",
      "#EF4444": "bg-red-500",
    };
    return colorMap[color] || "bg-gray-500";
  };

  return (
    <div className={`flex items-center space-x-2 ${className}`}>
      {/* 用户头像 */}
      <div
        className={`${
          classes.avatar
        } rounded-full flex items-center justify-center font-medium ${getLevelColorClass(
          userLevel.color
        )}`}
      >
        {user.avatar ? (
          // eslint-disable-next-line @next/next/no-img-element
          <img
            src={user.avatar}
            alt={user.username}
            className="w-full h-full rounded-full object-cover"
          />
        ) : (
          <span>{user.username.charAt(0).toUpperCase()}</span>
        )}
      </div>

      {/* 用户信息 */}
      <div className="flex-1 min-w-0">
        <div className="flex items-center space-x-2">
          <span
            className={`font-medium text-foreground truncate ${classes.text}`}
          >
            {user.nickname || user.username}
          </span>

          {/* 等级显示 */}
          {showLevel && (
            <span
              className={`${
                classes.badge
              } rounded-full font-medium whitespace-nowrap text-white ${getBadgeColorClass(
                userLevel.color
              )}`}
              title={`${userLevel.title} (等级 ${user.level})`}
              // 使用动态颜色类而不是内联样式
            >
              {userLevel.icon} Lv.{user.level}
            </span>
          )}
        </div>

        {/* 用户头衔 */}
        {showTitle && (user.title || userLevel.title) && (
          <div className={`${classes.text} text-secondary-text truncate`}>
            {user.title || userLevel.title}
          </div>
        )}
      </div>
    </div>
  );
}

// 简化版用户徽章组件，只显示头像和用户名
export function SimpleUserBadge({
  user,
  size = "sm",
  className = "",
}: {
  user: UserBasicInfo;
  size?: "sm" | "md" | "lg";
  className?: string;
}) {
  const sizeClasses = {
    sm: {
      avatar: "w-5 h-5 text-xs",
      text: "text-xs",
    },
    md: {
      avatar: "w-6 h-6 text-sm",
      text: "text-sm",
    },
    lg: {
      avatar: "w-8 h-8 text-base",
      text: "text-base",
    },
  };

  const classes = sizeClasses[size];

  return (
    <div className={`flex items-center space-x-1.5 ${className}`}>
      <div
        className={`${classes.avatar} bg-blue-100 dark:bg-blue-900/20 rounded-full flex items-center justify-center font-medium`}
      >
        {user.avatar ? (
          // eslint-disable-next-line @next/next/no-img-element
          <img
            src={user.avatar}
            alt={user.username}
            className="w-full h-full rounded-full object-cover"
          />
        ) : (
          <span className="text-blue-600">
            {user.username.charAt(0).toUpperCase()}
          </span>
        )}
      </div>
      <span className={`font-medium text-foreground ${classes.text}`}>
        {user.nickname || user.username}
      </span>
      {user.level > 1 && (
        <span className={`text-blue-600 ${classes.text}`}>Lv.{user.level}</span>
      )}
    </div>
  );
}

// 用户等级进度条组件
export function UserLevelProgress({
  currentLevel,
  currentPoints,
  nextLevel,
  pointsToNext,
  className = "",
}: {
  currentLevel: UserLevel;
  currentPoints: number;
  nextLevel?: UserLevel;
  pointsToNext?: number;
  className?: string;
}) {
  if (!nextLevel || !pointsToNext) {
    return (
      <div className={`${className}`}>
        <div className="flex items-center justify-between mb-2">
          <span
            className="text-sm font-medium"
            // 动态颜色必须使用内联样式
            // eslint-disable-next-line react/forbid-dom-props
            style={{ color: currentLevel.color }}
          >
            {currentLevel.icon} {currentLevel.title}
          </span>
          <span className="text-sm text-secondary-text">
            {currentPoints} 积分
          </span>
        </div>
        <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
          <div
            className="h-2 rounded-full"
            // 动态颜色必须使用内联样式
            // eslint-disable-next-line react/forbid-dom-props
            style={{ backgroundColor: currentLevel.color, width: "100%" }}
          />
        </div>
        <div className="text-xs text-secondary-text mt-1 text-center">
          已达到最高等级
        </div>
      </div>
    );
  }

  const totalPointsNeeded = nextLevel.min_points - currentLevel.min_points;
  const currentProgress = currentPoints - currentLevel.min_points;
  const progressPercentage = Math.min(
    (currentProgress / totalPointsNeeded) * 100,
    100
  );

  return (
    <div className={`${className}`}>
      <div className="flex items-center justify-between mb-2">
        <span
          className="text-sm font-medium"
          // 动态颜色必须使用内联样式
          // eslint-disable-next-line react/forbid-dom-props
          style={{ color: currentLevel.color }}
        >
          {currentLevel.icon} {currentLevel.title}
        </span>
        <span
          className="text-sm font-medium"
          // 动态颜色必须使用内联样式
          // eslint-disable-next-line react/forbid-dom-props
          style={{ color: nextLevel.color }}
        >
          {nextLevel.icon} {nextLevel.title}
        </span>
      </div>
      <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
        <div
          className="h-2 rounded-full transition-all duration-300"
          // 动态颜色和宽度必须使用内联样式
          // eslint-disable-next-line react/forbid-dom-props
          style={{
            backgroundColor: currentLevel.color,
            width: `${progressPercentage}%`,
          }}
        />
      </div>
      <div className="flex items-center justify-between text-xs text-secondary-text mt-1">
        <span>{currentPoints} 积分</span>
        <span>还需 {pointsToNext} 积分升级</span>
      </div>
    </div>
  );
}
